import React, { useEffect, useState } from 'react'

const HeroSection = () => {
  const [stars, setStars] = useState({ small: '', medium: '', big: '' })

  useEffect(() => {
    // Generate random star positions for box-shadow
    const generateStars = (count) => {
      let starsArray = []
      for (let i = 0; i < count; i++) {
        starsArray.push(`${Math.random() * 2000}px ${Math.random() * 2000}px #FFF`)
      }
      return starsArray.join(', ')
    }

    setStars({
      small: generateStars(700),
      medium: generateStars(200),
      big: generateStars(100)
    })
  }, [])

  return (
    <section className="gravity-hero-container">
      <div
        className="gravity-stars"
        style={{ boxShadow: stars.small }}
      ></div>
      <div
        className="gravity-stars2"
        style={{ boxShadow: stars.medium }}
      ></div>
      <div
        className="gravity-stars3"
        style={{ boxShadow: stars.big }}
      ></div>

      <div className="gravity-horizon">
        <div className="gravity-glow"></div>
      </div>

      <div className="gravity-earth"></div>

      <div className="gravity-title">GIVEN PROMISE <br/> ACADEMY</div>

      <div className="gravity-subtitle">
        <span>REACH </span>
        <span>FOR </span>
        <span>THE </span>
        <span>STARS</span>
      </div>
    </section>
  )
}

export default HeroSection

