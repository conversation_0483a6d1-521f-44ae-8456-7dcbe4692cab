import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import App from '../../App'

// Mock GSAP and ScrollTrigger
const mockScrollTrigger = {
  create: jest.fn(() => ({
    kill: jest.fn(),
    refresh: jest.fn(),
    progress: 0
  })),
  refresh: jest.fn()
}

jest.mock('gsap', () => ({
  gsap: {
    set: jest.fn(),
    to: jest.fn(() => ({ kill: jest.fn() })),
    registerPlugin: jest.fn(),
    timeline: jest.fn(() => ({
      to: jest.fn().mockReturnThis(),
      kill: jest.fn()
    }))
  },
  ScrollTrigger: mockScrollTrigger
}))

// Mock individual section components to avoid complex dependencies
jest.mock('../Header', () => () => <header data-testid="header">Header</header>)
jest.mock('../HeroSection', () => () => <section data-testid="hero">Hero</section>)
jest.mock('../MudSection', () => () => <section data-testid="mud">Mud</section>)
jest.mock('../ExploreSection', () => () => <section data-testid="explore">Explore</section>)
jest.mock('../ValuesSection', () => () => <section data-testid="values">Values</section>)
jest.mock('../HeadWelcomeSection', () => () => <section data-testid="head-welcome">Head Welcome</section>)
jest.mock('../SevenCsSection', () => () => <section data-testid="seven-cs">Seven Cs</section>)
jest.mock('../Year78Section', () => () => <section data-testid="year78">Year 78</section>)
jest.mock('../AnnouncementsSection', () => () => <section data-testid="announcements">Announcements</section>)
jest.mock('../TestimonialsSection', () => () => <section data-testid="testimonials">Testimonials</section>)
jest.mock('../SocialSection', () => () => <section data-testid="social">Social</section>)
jest.mock('../StatsSection', () => () => <section data-testid="stats">Stats</section>)
jest.mock('../ContactSection', () => () => <section data-testid="contact">Contact</section>)
jest.mock('../Footer', () => () => <footer data-testid="footer">Footer</footer>)

describe('Horizontal Scroll Integration', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()
    
    // Mock window properties
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })
    
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: 768,
    })
    
    // Mock matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    })

    // Mock performance API
    Object.defineProperty(window, 'performance', {
      writable: true,
      value: {
        now: jest.fn(() => Date.now())
      }
    })
  })

  test('renders complete app with horizontal scroll container', async () => {
    render(<App />)

    // Check that all main sections are present
    expect(screen.getByTestId('header')).toBeInTheDocument()
    expect(screen.getByTestId('hero')).toBeInTheDocument()
    expect(screen.getByTestId('mud')).toBeInTheDocument()
    expect(screen.getByTestId('explore')).toBeInTheDocument()
    expect(screen.getByTestId('values')).toBeInTheDocument()
    
    // Check horizontal scroll sections
    expect(screen.getByTestId('head-welcome')).toBeInTheDocument()
    expect(screen.getByTestId('seven-cs')).toBeInTheDocument()
    expect(screen.getByTestId('year78')).toBeInTheDocument()
    
    // Check remaining sections
    expect(screen.getByTestId('announcements')).toBeInTheDocument()
    expect(screen.getByTestId('footer')).toBeInTheDocument()
  })

  test('horizontal scroll container is properly configured', async () => {
    render(<App />)

    // Find the horizontal scroll container
    const horizontalContainer = screen.getByRole('region', { 
      name: /horizontal scrolling sections/i 
    })
    
    expect(horizontalContainer).toBeInTheDocument()
    expect(horizontalContainer).toHaveAttribute('tabIndex', '0')
    expect(horizontalContainer).toHaveAttribute('role', 'region')
  })

  test('keyboard navigation works in horizontal container', async () => {
    render(<App />)

    const horizontalContainer = screen.getByRole('region', { 
      name: /horizontal scrolling sections/i 
    })

    // Test keyboard navigation
    fireEvent.keyDown(horizontalContainer, { key: 'ArrowRight' })
    fireEvent.keyDown(horizontalContainer, { key: 'ArrowLeft' })
    fireEvent.keyDown(horizontalContainer, { key: 'Home' })
    fireEvent.keyDown(horizontalContainer, { key: 'End' })

    // Should not throw errors
    expect(horizontalContainer).toBeInTheDocument()
  })

  test('GSAP ScrollTrigger is initialized', async () => {
    render(<App />)

    // Wait for component to mount and useEffect to run
    await waitFor(() => {
      // ScrollTrigger.create should have been called
      expect(mockScrollTrigger.create).toHaveBeenCalled()
    }, { timeout: 1000 })
  })

  test('mobile fallback works correctly', async () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 600,
    })

    render(<App />)

    // In mobile mode, sections should still be present
    expect(screen.getByTestId('head-welcome')).toBeInTheDocument()
    expect(screen.getByTestId('seven-cs')).toBeInTheDocument()
    expect(screen.getByTestId('year78')).toBeInTheDocument()

    // ScrollTrigger should not be created for mobile
    await waitFor(() => {
      expect(mockScrollTrigger.create).not.toHaveBeenCalled()
    }, { timeout: 500 })
  })

  test('progress indicator is present', async () => {
    render(<App />)

    // Progress indicator should be in the DOM
    const progressIndicator = document.querySelector('.fixed.bottom-8')
    expect(progressIndicator).toBeInTheDocument()
  })

  test('error boundary handles errors gracefully', async () => {
    // Mock console.error to avoid noise
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    // Force an error in GSAP
    jest.doMock('gsap', () => {
      throw new Error('GSAP loading error')
    })

    render(<App />)

    // App should still render without crashing
    expect(screen.getByTestId('header')).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })

  test('accessibility features are present', async () => {
    render(<App />)

    const horizontalContainer = screen.getByRole('region')
    
    // Check ARIA attributes
    expect(horizontalContainer).toHaveAttribute('aria-label')
    expect(horizontalContainer).toHaveAttribute('aria-describedby')
    
    // Check screen reader instructions
    const instructions = document.getElementById('horizontal-scroll-instructions')
    expect(instructions).toBeInTheDocument()
    expect(instructions).toHaveClass('sr-only')
  })
})
