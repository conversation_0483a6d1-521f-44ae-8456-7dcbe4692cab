import React, { useEffect, useRef } from 'react'
import { gsap } from 'gsap'

const ScrollProgressIndicator = ({ 
  progress = 0, 
  totalSections = 3, 
  currentSection = 0, 
  className = '',
  isActive = false,
  style = 'dots' // 'dots' or 'bar'
}) => {
  const containerRef = useRef(null)
  const progressBarRef = useRef(null)
  const dotsRef = useRef([])

  // Animate visibility based on isActive state with auto-hide
  useEffect(() => {
    if (!containerRef.current) return

    const timeline = gsap.timeline()

    if (isActive) {
      timeline
        .to(containerRef.current, {
          opacity: 1,
          y: 0,
          duration: 0.6,
          ease: 'power2.out'
        })
        .to(containerRef.current, {
          scale: 1,
          duration: 0.3,
          ease: 'back.out(1.7)'
        }, '-=0.3')
    } else {
      timeline.to(containerRef.current, {
        opacity: 0,
        y: 20,
        scale: 0.9,
        duration: 0.4,
        ease: 'power2.in'
      })
    }

    return () => timeline.kill()
  }, [isActive])

  // Animate progress bar
  useEffect(() => {
    if (style === 'bar' && progressBarRef.current) {
      gsap.to(progressBarRef.current, {
        scaleX: progress,
        duration: 0.3,
        ease: 'power2.out'
      })
    }
  }, [progress, style])

  // Animate dots with enhanced transitions
  useEffect(() => {
    if (style === 'dots' && dotsRef.current.length > 0) {
      dotsRef.current.forEach((dot, index) => {
        if (!dot) return

        const isActive = index === currentSection
        const isPassed = index < currentSection
        const isFuture = index > currentSection

        // Create staggered animation timeline
        const timeline = gsap.timeline()

        timeline.to(dot, {
          scale: isActive ? 1.3 : isPassed ? 1.1 : 1,
          backgroundColor: isActive ? '#e91e63' : isPassed ? '#c2185b' : '#e0e0e0',
          boxShadow: isActive ? '0 0 20px rgba(233, 30, 99, 0.5)' : 'none',
          duration: 0.4,
          ease: 'power2.out',
          delay: index * 0.05 // Stagger effect
        })

        // Add pulse effect for active dot
        if (isActive) {
          timeline.to(dot, {
            scale: 1.2,
            duration: 0.2,
            ease: 'power2.inOut',
            yoyo: true,
            repeat: 1
          })
        }
      })
    }
  }, [currentSection, style])

  const renderDots = () => (
    <div className="flex space-x-3">
      {Array.from({ length: totalSections }, (_, index) => (
        <div
          key={index}
          ref={el => dotsRef.current[index] = el}
          className="w-3 h-3 rounded-full transition-all duration-300 cursor-pointer"
          style={{
            backgroundColor: '#e0e0e0',
            transform: 'scale(1)'
          }}
          onClick={() => {
            // Optional: Add click navigation functionality
            const targetProgress = index / (totalSections - 1)
            // This would need to be connected to scroll functionality
          }}
        />
      ))}
    </div>
  )

  const renderBar = () => (
    <div className="w-32 h-1 bg-gray-300 rounded-full overflow-hidden">
      <div
        ref={progressBarRef}
        className="h-full bg-gradient-to-r from-[#e91e63] to-[#c2185b] origin-left"
        style={{
          transform: 'scaleX(0)'
        }}
      />
    </div>
  )

  return (
    <div
      ref={containerRef}
      className={`fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 ${className}`}
      style={{
        opacity: 0,
        transform: 'translateX(-50%) translateY(20px)'
      }}
    >
      <div className="bg-white/90 backdrop-blur-sm rounded-full px-6 py-3 shadow-lg border border-gray-200">
        {style === 'dots' ? renderDots() : renderBar()}
      </div>
      
      {/* Section labels */}
      <div className="flex justify-between mt-2 text-xs text-gray-600 px-2">
        <span>Head's Welcome</span>
        <span>7Cs</span>
        <span>Year 7&8</span>
      </div>
    </div>
  )
}

export default ScrollProgressIndicator
