import { useEffect, useRef, useState, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger);

/**
 * Custom hook for managing horizontal scroll animations with GSAP ScrollTrigger
 * @param {Object} config - Configuration object
 * @param {React.RefObject} config.containerRef - Reference to the container element
 * @param {React.RefObject} config.sectionsRef - Reference to the sections container
 * @param {Function} config.onProgressChange - Callback for progress changes
 * @param {Function} config.onComplete - Callback when scrolling is complete
 * @param {boolean} config.enabled - Whether the horizontal scroll is enabled
 * @returns {Object} Hook state and methods
 */
export const useHorizontalScroll = ({
  containerRef,
  sectionsRef,
  onProgressChange,
  onComplete,
  enabled = true
}) => {
  const [progress, setProgress] = useState(0);
  const [isActive, setIsActive] = useState(false);
  const [currentSection, setCurrentSection] = useState(0);
  const scrollTriggerRef = useRef(null);
  const animationRef = useRef(null);

  // Default configuration
  const config = {
    duration: 0.8,
    ease: "power2.inOut",
    threshold: 0.1,
    mobileBreakpoint: 768,
    debugMode: false
  };

  // Check if we're on mobile
  const isMobile = useCallback(() => {
    return window.innerWidth < config.mobileBreakpoint;
  }, [config.mobileBreakpoint]);

  // Calculate current section based on progress
  const calculateCurrentSection = useCallback((progressValue) => {
    const totalSections = 3; // HeadWelcomeSection, SevenCsSection, Year78Section
    const sectionIndex = Math.floor(progressValue * totalSections);
    return Math.min(sectionIndex, totalSections - 1);
  }, []);

  // Update progress and related state
  const updateProgress = useCallback((progressValue) => {
    setProgress(progressValue);
    const newSection = calculateCurrentSection(progressValue);
    
    if (newSection !== currentSection) {
      setCurrentSection(newSection);
    }

    // Call progress change callback if provided
    if (onProgressChange) {
      onProgressChange(progressValue);
    }

    // Check if scrolling is complete
    if (progressValue >= 1 && onComplete) {
      onComplete();
    }
  }, [currentSection, onProgressChange, onComplete, calculateCurrentSection]);

  // Initialize ScrollTrigger
  const initializeScrollTrigger = useCallback(() => {
    if (!containerRef.current || !sectionsRef.current || !enabled || isMobile()) {
      return;
    }

    const container = containerRef.current;
    const sections = sectionsRef.current;
    
    // Calculate total width for horizontal scroll
    const totalWidth = sections.scrollWidth;
    const viewportWidth = window.innerWidth;
    const scrollDistance = totalWidth - viewportWidth;

    if (config.debugMode) {
      console.log('Initializing horizontal scroll:', {
        totalWidth,
        viewportWidth,
        scrollDistance
      });
    }

    // Create the horizontal scroll animation
    animationRef.current = gsap.to(sections, {
      x: -scrollDistance,
      ease: "none",
      duration: 1
    });

    // Create ScrollTrigger instance
    scrollTriggerRef.current = ScrollTrigger.create({
      trigger: container,
      start: "top top",
      end: () => `+=${scrollDistance}`,
      scrub: 1,
      pin: true,
      animation: animationRef.current,
      onUpdate: (self) => {
        const progressValue = self.progress;
        updateProgress(progressValue);
        setIsActive(progressValue > 0 && progressValue < 1);
      },
      onToggle: (self) => {
        setIsActive(self.isActive);
      },
      invalidateOnRefresh: true
    });

    if (config.debugMode) {
      console.log('ScrollTrigger initialized:', scrollTriggerRef.current);
    }

  }, [containerRef, sectionsRef, enabled, updateProgress, config.debugMode, isMobile]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (scrollTriggerRef.current) {
      scrollTriggerRef.current.kill();
      scrollTriggerRef.current = null;
    }
    
    if (animationRef.current) {
      animationRef.current.kill();
      animationRef.current = null;
    }

    if (config.debugMode) {
      console.log('ScrollTrigger cleaned up');
    }
  }, [config.debugMode]);

  // Refresh ScrollTrigger (useful for responsive changes)
  const refresh = useCallback(() => {
    if (scrollTriggerRef.current) {
      ScrollTrigger.refresh();
    }
  }, []);

  // Initialize on mount and when dependencies change
  useEffect(() => {
    if (!enabled) return;

    // Small delay to ensure DOM is ready
    const timer = setTimeout(() => {
      initializeScrollTrigger();
    }, 100);

    return () => {
      clearTimeout(timer);
      cleanup();
    };
  }, [initializeScrollTrigger, cleanup, enabled]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      // Cleanup and reinitialize on resize
      cleanup();
      setTimeout(() => {
        initializeScrollTrigger();
      }, 100);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [cleanup, initializeScrollTrigger]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    progress,
    isActive,
    currentSection,
    refresh,
    cleanup
  };
};

export default useHorizontalScroll;