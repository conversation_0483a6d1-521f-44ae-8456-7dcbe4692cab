import React, { useRef, useEffect } from 'react'
import { ScrollReveal } from './ScrollAnimations'
import { gsap } from 'gsap'
import exploreImage from '../assets/images/Vector-Smart-Object-02.jpg'

const ExploreSection = () => {
  const loadingRef = useRef()

  useEffect(() => {
    // Spinning animation for loading indicator
    gsap.to(loadingRef.current, {
      rotation: 360,
      duration: 2,
      ease: "none",
      repeat: -1
    })
  }, [])

  return (
    <section className="py-16 bg-[#e91e63]">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          {/* Left side - Image */}
          <ScrollReveal animationType="fadeRight">
            <div className="relative">
              <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden hover:scale-105 transition-transform duration-300 shadow-lg">
                <img 
                  src={exploreImage} 
                  alt="Students exploring and discovering at Great Walstead School"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <div className="text-2xl mb-1">🔍</div>
                  <div className="text-sm font-medium font-color-white">Explore with Us</div>
                </div>
              </div>
            </div>
          </ScrollReveal>
          
          {/* Right side - Text content */}
          <ScrollReveal animationType="fadeLeft" delay={0.2}>
            <div>
              <h2 className=" text-white font-['TrueNorthRoughBlack'] text-4xl md:text-5xl font-bold  mb-6">
                Explore with us
              </h2>
              <p className="text-lg text-white mb-8">
                Come on a secret mission to discover the sparks of magic that make Great Walstead so unique.
              </p>
              
            </div>
          </ScrollReveal>
        </div>
      </div>
    </section>
  )
}

export default ExploreSection

