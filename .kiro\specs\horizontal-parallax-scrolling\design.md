# Design Document

## Overview

The horizontal parallax scrolling system will transform the current vertical layout of three key sections (HeadWelcomeSection, SevenCsSection, Year78Section) into an immersive horizontal scrolling experience. The system will use GSAP's ScrollTrigger plugin to create smooth, performant animations that respond to user scroll input.

The design follows a container-based approach where the three sections are placed horizontally within a wrapper, and scroll events are intercepted to translate the container horizontally instead of allowing normal vertical scrolling. Once all horizontal sections are viewed, normal vertical scrolling resumes to the AnnouncementsSection.

## Architecture

### Core Components

1. **HorizontalScrollContainer**: A wrapper component that contains the three horizontal sections
2. **useHorizontalScroll**: A custom React hook that manages GSAP ScrollTrigger logic
3. **ScrollProgressIndicator**: A visual indicator showing progress through horizontal sections
4. **ScrollDebugger**: Development utility for debugging scroll states

### Component Hierarchy

```
App
├── Header
├── HeroSection
├── MudSection
├── ExploreSection
├── ValuesSection
├── HorizontalScrollContainer
│   ├── ScrollProgressIndicator
│   ├── HeadWelcomeSection
│   ├── SevenCsSection
│   └── Year78Section
├── AnnouncementsSection
└── ... (other sections)
```

### GSAP Integration

The system leverages GSAP's ScrollTrigger plugin with the following configuration:
- **Trigger Element**: The HorizontalScrollContainer
- **Animation**: Horizontal translation of the sections container
- **Scrub**: True (for smooth scroll-linked animation)
- **Pin**: True (to pin the container during horizontal scroll)
- **End**: Based on total horizontal width of all sections

## Components and Interfaces

### HorizontalScrollContainer Component

```jsx
interface HorizontalScrollContainerProps {
  children: React.ReactNode;
  className?: string;
  showProgressIndicator?: boolean;
}

const HorizontalScrollContainer: React.FC<HorizontalScrollContainerProps>
```

**Responsibilities:**
- Wraps the three horizontal sections in a flex container
- Applies necessary CSS for horizontal layout
- Integrates with the useHorizontalScroll hook
- Manages responsive behavior and mobile fallbacks

### useHorizontalScroll Hook

```jsx
interface HorizontalScrollConfig {
  containerRef: React.RefObject<HTMLElement>;
  sectionsRef: React.RefObject<HTMLElement>;
  onProgressChange?: (progress: number) => void;
  onComplete?: () => void;
  enabled?: boolean;
}

const useHorizontalScroll: (config: HorizontalScrollConfig) => {
  progress: number;
  isActive: boolean;
  currentSection: number;
}
```

**Responsibilities:**
- Initializes GSAP ScrollTrigger
- Manages scroll event handling and debouncing
- Calculates and reports scroll progress
- Handles cleanup on component unmount
- Provides responsive breakpoint handling

### ScrollProgressIndicator Component

```jsx
interface ScrollProgressIndicatorProps {
  progress: number;
  totalSections: number;
  currentSection: number;
  className?: string;
}

const ScrollProgressIndicator: React.FC<ScrollProgressIndicatorProps>
```

**Responsibilities:**
- Displays current progress through horizontal sections
- Shows section indicators (dots or bars)
- Animates progress changes smoothly
- Auto-hides when horizontal scrolling is complete

## Data Models

### ScrollState

```typescript
interface ScrollState {
  progress: number; // 0-1 representing scroll progress
  currentSection: number; // 0-2 for the three sections
  isActive: boolean; // whether horizontal scrolling is active
  direction: 'forward' | 'backward'; // scroll direction
}
```

### ScrollConfig

```typescript
interface ScrollConfig {
  duration: number; // animation duration in seconds
  ease: string; // GSAP easing function
  threshold: number; // scroll threshold to trigger section change
  mobileBreakpoint: number; // pixel width for mobile fallback
  debugMode: boolean; // enable debug logging
}
```

### SectionMetadata

```typescript
interface SectionMetadata {
  id: string;
  component: React.ComponentType;
  width: number; // section width in viewport units
  backgroundColor?: string;
}
```

## Error Handling

### GSAP Loading Errors
- **Detection**: Check if GSAP and ScrollTrigger are available
- **Fallback**: Gracefully degrade to vertical scrolling
- **User Feedback**: Console warning for developers

### Performance Issues
- **Detection**: Monitor frame rate and scroll performance
- **Mitigation**: Reduce animation complexity or disable on low-end devices
- **Fallback**: Switch to CSS-only transitions

### Mobile/Touch Device Handling
- **Detection**: Use media queries and touch event detection
- **Adaptation**: Adjust scroll sensitivity and animation timing
- **Fallback**: Vertical scrolling on screens below 768px width

### Browser Compatibility
- **Detection**: Feature detection for required APIs
- **Polyfills**: Include necessary polyfills for older browsers
- **Fallback**: Standard vertical layout for unsupported browsers

## Testing Strategy

### Unit Tests
- **useHorizontalScroll Hook**: Test scroll calculations, progress updates, and cleanup
- **ScrollProgressIndicator**: Test progress display and section transitions
- **HorizontalScrollContainer**: Test responsive behavior and prop handling

### Integration Tests
- **GSAP Integration**: Test ScrollTrigger initialization and cleanup
- **Scroll Behavior**: Test horizontal scroll transitions and vertical scroll resumption
- **Responsive Behavior**: Test mobile fallbacks and breakpoint handling

### Performance Tests
- **Animation Performance**: Monitor FPS during scroll animations
- **Memory Usage**: Test for memory leaks during scroll events
- **Load Time Impact**: Measure impact on initial page load

### Cross-Browser Tests
- **Desktop Browsers**: Chrome, Firefox, Safari, Edge
- **Mobile Browsers**: iOS Safari, Chrome Mobile, Samsung Internet
- **Accessibility**: Test with screen readers and keyboard navigation

### Visual Regression Tests
- **Section Transitions**: Capture screenshots of scroll states
- **Progress Indicator**: Test visual states across different progress levels
- **Responsive Layouts**: Test appearance across different screen sizes

## Implementation Considerations

### Performance Optimizations
- Use `will-change: transform` CSS property on animated elements
- Implement scroll event debouncing to prevent excessive calculations
- Use GSAP's `force3D: true` for hardware acceleration
- Lazy load section content that's not immediately visible

### Accessibility
- Provide keyboard navigation alternatives (arrow keys, tab navigation)
- Include ARIA labels for progress indicators
- Respect `prefers-reduced-motion` user preference
- Ensure focus management during horizontal transitions

### SEO Considerations
- Maintain semantic HTML structure within sections
- Ensure content remains crawlable despite horizontal layout
- Preserve heading hierarchy and document outline
- Include proper meta descriptions for section content

### Mobile Experience
- Implement touch gesture support for horizontal swiping
- Adjust animation timing for touch interactions
- Provide visual feedback for swipe gestures
- Ensure sections remain readable on small screens

## Configuration Options

### Default Configuration
```javascript
const defaultConfig = {
  duration: 0.8,
  ease: "power2.inOut",
  threshold: 0.1,
  mobileBreakpoint: 768,
  debugMode: false,
  showProgressIndicator: true,
  enableKeyboardNavigation: true,
  respectReducedMotion: true
}
```

### Customization Points
- Animation timing and easing functions
- Progress indicator styling and positioning
- Mobile breakpoint and fallback behavior
- Debug logging and development tools
- Section transition effects and timing