import React, { useRef, useEffect, useState, useCallback } from 'react'
import { useHorizontalScroll } from './ScrollAnimations'
import ScrollProgressIndicator from './ScrollProgressIndicator'
import ScrollErrorBoundary from './ScrollErrorBoundary'

const HorizontalScrollContainerInner = ({
  children,
  className = '',
  showProgressIndicator = true,
  debugMode = false,
  mobileBreakpoint = 768,
  onProgressChange,
  onComplete
}) => {
  const containerRef = useRef(null)
  const sectionsRef = useRef(null)
  const [isMobile, setIsMobile] = useState(false)
  const [focusedSection, setFocusedSection] = useState(0)
  const [touchState, setTouchState] = useState({
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
    isTouch: false
  })

  // Check for mobile on mount and resize
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < mobileBreakpoint
      setIsMobile(mobile)

      if (debugMode) {
        console.log('[HorizontalScrollContainer] Mobile check:', mobile)
      }
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [mobileBreakpoint, debugMode])

  // Touch event handlers
  const handleTouchStart = useCallback((e) => {
    if (isMobile) return // Skip touch handling on mobile (use vertical scroll)

    const touch = e.touches[0]
    setTouchState({
      startX: touch.clientX,
      startY: touch.clientY,
      currentX: touch.clientX,
      currentY: touch.clientY,
      isTouch: true
    })

    if (debugMode) {
      console.log('[HorizontalScrollContainer] Touch start:', touch.clientX, touch.clientY)
    }
  }, [isMobile, debugMode])

  const handleTouchMove = useCallback((e) => {
    if (isMobile || !touchState.isTouch) return

    const touch = e.touches[0]
    const deltaX = touch.clientX - touchState.startX
    const deltaY = touch.clientY - touchState.startY

    // Prevent vertical scrolling if horizontal swipe is detected
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      e.preventDefault()
    }

    setTouchState(prev => ({
      ...prev,
      currentX: touch.clientX,
      currentY: touch.clientY
    }))
  }, [isMobile, touchState.isTouch, touchState.startX, touchState.startY])

  const handleTouchEnd = useCallback(() => {
    if (isMobile || !touchState.isTouch) return

    const deltaX = touchState.currentX - touchState.startX
    const deltaY = touchState.currentY - touchState.startY

    // Check if it's a horizontal swipe
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      // Simulate scroll event for horizontal navigation
      const direction = deltaX > 0 ? 'right' : 'left'

      if (debugMode) {
        console.log('[HorizontalScrollContainer] Horizontal swipe detected:', direction)
      }

      // You could trigger section navigation here
      // For now, we'll let the ScrollTrigger handle it
    }

    setTouchState({
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
      isTouch: false
    })
  }, [isMobile, touchState, debugMode])

  // Keyboard navigation handler
  const handleKeyDown = useCallback((e) => {
    if (isMobile) return

    const childrenCount = React.Children.count(children)

    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault()
        setFocusedSection(prev => Math.max(0, prev - 1))
        if (debugMode) {
          console.log('[HorizontalScrollContainer] Keyboard: Previous section')
        }
        break
      case 'ArrowRight':
        e.preventDefault()
        setFocusedSection(prev => Math.min(childrenCount - 1, prev + 1))
        if (debugMode) {
          console.log('[HorizontalScrollContainer] Keyboard: Next section')
        }
        break
      case 'Home':
        e.preventDefault()
        setFocusedSection(0)
        if (debugMode) {
          console.log('[HorizontalScrollContainer] Keyboard: First section')
        }
        break
      case 'End':
        e.preventDefault()
        setFocusedSection(childrenCount - 1)
        if (debugMode) {
          console.log('[HorizontalScrollContainer] Keyboard: Last section')
        }
        break
      default:
        break
    }
  }, [isMobile, children, debugMode])

  // Focus management
  useEffect(() => {
    if (!isMobile && containerRef.current) {
      containerRef.current.focus()
    }
  }, [isMobile, focusedSection])

  // Use the horizontal scroll hook
  const scrollState = useHorizontalScroll({
    containerRef,
    sectionsRef,
    onProgressChange,
    onComplete,
    enabled: !isMobile,
    mobileBreakpoint,
    debugMode
  })

  // Convert children to array for easier handling
  const childrenArray = React.Children.toArray(children)

  if (debugMode) {
    console.log('[HorizontalScrollContainer] Render state:', {
      isMobile,
      scrollState,
      childrenCount: childrenArray.length
    })
  }

  return (
    <div
      ref={containerRef}
      className={`horizontal-scroll-container ${className}`}
      style={{
        position: 'relative',
        overflow: isMobile ? 'visible' : 'hidden'
      }}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onKeyDown={handleKeyDown}
      tabIndex={isMobile ? -1 : 0}
      role="region"
      aria-label="Horizontal scrolling sections"
      aria-describedby="horizontal-scroll-instructions"
    >
      {/* Screen reader instructions */}
      <div
        id="horizontal-scroll-instructions"
        className="sr-only"
        style={{
          position: 'absolute',
          width: '1px',
          height: '1px',
          padding: 0,
          margin: '-1px',
          overflow: 'hidden',
          clip: 'rect(0, 0, 0, 0)',
          whiteSpace: 'nowrap',
          border: 0
        }}
      >
        Use arrow keys to navigate between sections horizontally.
        Press Home to go to first section, End to go to last section.
      </div>
      {/* Mobile fallback - vertical layout */}
      {isMobile ? (
        <div
          className="mobile-vertical-layout"
          style={{
            display: 'block',
            width: '100%'
          }}
        >
          {childrenArray.map((child, index) => (
            <div
              key={index}
              className="mobile-section"
              style={{
                width: '100%',
                minHeight: 'auto',
                padding: '2rem 0'
              }}
            >
              {child}
            </div>
          ))}
        </div>
      ) : (
        /* Desktop horizontal layout */
        <div 
          ref={sectionsRef}
          className="horizontal-sections"
          style={{
            display: 'flex',
            height: '100vh',
            willChange: 'transform'
          }}
        >
          {childrenArray.map((child, index) => (
            <div
              key={index}
              className="horizontal-section"
              style={{
                width: '100vw',
                height: '100vh',
                flexShrink: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              role="tabpanel"
              aria-label={`Section ${index + 1} of ${childrenArray.length}`}
              aria-hidden={focusedSection !== index}
              tabIndex={focusedSection === index ? 0 : -1}
            >
              <div className="section-content" style={{ width: '100%', height: '100%' }}>
                {child}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Progress Indicator */}
      {showProgressIndicator && !isMobile && (
        <ScrollProgressIndicator
          progress={scrollState.progress}
          totalSections={childrenArray.length}
          currentSection={scrollState.currentSection}
          isActive={scrollState.isActive}
          style="dots"
        />
      )}

      {/* Debug information */}
      {debugMode && !isMobile && (
        <div
          className="debug-info"
          style={{
            position: 'fixed',
            top: '10px',
            right: '10px',
            background: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '10px',
            borderRadius: '5px',
            fontSize: '12px',
            zIndex: 9999,
            fontFamily: 'monospace'
          }}
        >
          <div>Progress: {(scrollState.progress * 100).toFixed(1)}%</div>
          <div>Section: {scrollState.currentSection + 1}/{childrenArray.length}</div>
          <div>Active: {scrollState.isActive ? 'Yes' : 'No'}</div>
          <div>Direction: {scrollState.direction}</div>
        </div>
      )}
    </div>
  )
}

// Wrapper component with error boundary
const HorizontalScrollContainer = (props) => {
  return (
    <ScrollErrorBoundary
      showErrorDetails={props.debugMode}
      onError={(error, errorInfo) => {
        console.error('[HorizontalScrollContainer] Error caught by boundary:', error, errorInfo)
      }}
      fallback={
        <div className="vertical-scroll-fallback">
          {props.children}
        </div>
      }
    >
      <HorizontalScrollContainerInner {...props} />
    </ScrollErrorBoundary>
  )
}

export default HorizontalScrollContainer
