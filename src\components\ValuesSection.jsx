import React from 'react'
import valuesImage from '../assets/images/values.webp'

const ValuesSection = () => {
  return (
    <section className="relative z-0 w-full min-h-screen flex flex-wrap md:items-center py-24 md:pt-32 bg-white">
      {/* Left side content */}
      <div className="px-6 md:px-4 md:w-1/2 z-10">
        <div className="space-y-4 md:max-w-[32rem] md:mx-auto">
          <h3 className="text-4xl md:text-5xl font-bold text-pink-600 text-balance font-['TrueNorthRoughBlack']">
            Our values
          </h3>
          <div className="prose text-base font-normal md:max-w-md">
            <p className="text-gray-700 leading-relaxed">
              We lead by example to instil positive values – empathy, adventure and determination –
              which are integral to our school life and just as important to us as academic achievement.
            </p>
          </div>
          <div className="flex flex-wrap items-center gap-4 lg:gap-6">
            <a
              className="inline-block px-8 py-3 border-2 border-pink-600 text-pink-600 font-semibold rounded-full hover:bg-pink-600 hover:text-white transition-colors duration-300"
              href="#"
            >
              Discover more
            </a>
          </div>
        </div>
      </div>

      {/* Right side illustration */}
      <div className="hidden md:flex absolute left-[40%] w-1/2 max-w-3xl top-1/2 -translate-y-1/2 flex-col">
        <div className="w-full flex justify-center items-center">
          <div className="relative w-96 h-96">
            <img
              src={valuesImage}
              alt="Values illustration"
              className="w-full h-full object-cover rounded-lg"
            />
          </div>
        </div>
      </div>

      {/* Bottom wave decoration */}
      <div className="absolute bottom-0 left-0 w-full h-16 overflow-hidden">
        <svg className="absolute bottom-0 left-0 w-full h-full" viewBox="0 0 1200 64" preserveAspectRatio="none">
          <path
            d="M0,32 C100,10 200,54 300,32 C400,10 500,54 600,32 C700,10 800,54 900,32 C1000,10 1100,54 1200,32 L1200,64 L0,64 Z"
            fill="#e91e63"
          />
        </svg>
      </div>
    </section>
  )
}

export default ValuesSection