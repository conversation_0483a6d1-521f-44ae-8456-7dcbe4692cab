import React from 'react'

class ScrollErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null 
    }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log the error for debugging
    console.error('[ScrollErrorBoundary] Scroll animation error:', error, errorInfo)
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    })

    // Optional: Send error to logging service
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI
      return (
        <div className="scroll-error-fallback">
          {this.props.fallback || (
            <div className="vertical-scroll-fallback">
              <div className="error-message" style={{
                padding: '2rem',
                textAlign: 'center',
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '8px',
                margin: '2rem 0'
              }}>
                <h3 style={{ color: '#6c757d', marginBottom: '1rem' }}>
                  Scroll Animation Unavailable
                </h3>
                <p style={{ color: '#6c757d', marginBottom: '1rem' }}>
                  The horizontal scroll feature encountered an error. 
                  Content is displayed in standard vertical layout.
                </p>
                {this.props.showErrorDetails && this.state.error && (
                  <details style={{ marginTop: '1rem', textAlign: 'left' }}>
                    <summary style={{ cursor: 'pointer', color: '#dc3545' }}>
                      Error Details (Development)
                    </summary>
                    <pre style={{ 
                      fontSize: '12px', 
                      backgroundColor: '#f8f9fa', 
                      padding: '1rem',
                      borderRadius: '4px',
                      overflow: 'auto',
                      marginTop: '0.5rem'
                    }}>
                      {this.state.error.toString()}
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
              
              {/* Render children in vertical layout as fallback */}
              <div className="vertical-fallback-content">
                {this.props.children}
              </div>
            </div>
          )}
        </div>
      )
    }

    return this.props.children
  }
}

export default ScrollErrorBoundary
