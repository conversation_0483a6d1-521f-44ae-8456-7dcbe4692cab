import React, { useState, useEffect, useRef } from 'react';
import { gsap } from 'gsap';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false); // New state for search toggle
  const headerRef = useRef(null);
  const logoRef = useRef(null);
  const navRef = useRef(null); // This will now wrap the new UL

  useEffect(() => {
    // Initial header animation
    const tl = gsap.timeline();
    
    gsap.set([logoRef.current, navRef.current], { opacity: 0, y: -20 });
    
    tl.to(logoRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    })
    .to(navRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6");

    // Header scroll effect
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      
      if (currentScrollY > 100) {
        gsap.to(headerRef.current, {
          backgroundColor: "rgba(26, 43, 74, 0.95)",
          backdropFilter: "blur(10px)",
          duration: 0.3
        });
      } else {
        gsap.to(headerRef.current, {
          backgroundColor: "rgba(26, 43, 74, 0.8)",
          backdropFilter: "blur(0px)",
          duration: 0.3
        });
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    const newMenuState = !isMenuOpen;
    setIsMenuOpen(newMenuState);

    // Close search when opening menu
    if (newMenuState) {
      setIsSearchOpen(false);
    }
    
    // GSAP animation for the mobile menu
    if (newMenuState) {
      gsap.to('.mobile-menu', {
        opacity: 1,
        y: 0,
        duration: 0.3,
        ease: "power2.out",
        display: 'block'
      });
    } else {
      gsap.to('.mobile-menu', {
        opacity: 0,
        y: -20,
        duration: 0.3,
        ease: "power2.in",
        onComplete: () => {
          gsap.set('.mobile-menu', { display: 'none' });
        }
      });
    }
  };
  
  const toggleSearch = () => {
    const newSearchState = !isSearchOpen;
    setIsSearchOpen(newSearchState);

    // Close menu when opening search
    if (newSearchState && isMenuOpen) {
      toggleMenu(); // This will correctly reverse the menu state and animation
    }
  };


  return (
    <>
      {/* Header Bar */}
      <header 
        ref={headerRef}
        className="bg-[#1a2b4a]/80 text-white fixed top-0 left-0 right-0 z-50"
      >
        <div className="flex items-center justify-between px-4 py-2">
          {/* Left side - Logo */}
          <div ref={logoRef} className="flex items-center">
            <img 
              src="/images/logo.png" 
              alt="Logo" 
              className="h-20 w-auto object-contain"
            />
          </div>
          
          {/* Right side - Replicated Menu icons */}
          <div ref={navRef}>
            <ul className="flex justify-end items-start gap-x-6">
              <li className="pt-4">
                <button 
                  className="appearance-none block overflow-hidden w-7 aspect-square pl-[3px] pt-[3px] pb-px pr-px bg-primary text-white stroke-current rounded-full" 
                  role="button" 
                  onClick={toggleSearch} 
                  aria-label="Toggle search"
                  style={{backgroundColor: '#e91e63'}} // Added primary color from your old code
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="4" stroke="currentColor" className="w-full">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"></path>
                  </svg>
                </button>
              </li>
              <li>
                <button 
                  className="relative block w-28 -mr-8 -mt-7 z-20 text-white focus:outline-none" 
                  aria-label="Toggle navigation" 
                  onClick={toggleMenu}
                >
                  <img src="/images/btn-burger.png" className="w-full h-auto" alt="Burger Menu Icon" width="112" height="103" />
                  <span className="absolute top-[45%] left-[40%] -translate-x-1/2 font-heading text-base tracking-wider">
                    {isMenuOpen ? 'Close' : 'Menu'}
                  </span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </header>

      {/* Mobile menu overlay */}
      <div className="mobile-menu fixed inset-0 bg-[#1a2b4a] z-40 pt-24 opacity-0 transform translate-y-[-20px] hidden">
        <nav className="px-6 py-8">
          <ul className="space-y-6 text-white text-lg text-center">
            <li><a href="#" className="hover:text-[#e91e63] transition-colors">Home</a></li>
            <li><a href="#" className="hover:text-[#e91e63] transition-colors">Admissions</a></li>
            <li><a href="#" className="hover:text-[#e91e63] transition-colors">School Life</a></li>
            <li><a href="#" className="hover:text-[#e91e63] transition-colors">News & Events</a></li>
            <li><a href="#" className="hover:text-[#e91e63] transition-colors">Contact</a></li>
             <li className="pt-4">
                <a href="#" className="bg-[#e91e63] text-white px-6 py-3 rounded text-sm font-medium hover:bg-[#c2185b] transition-colors transform hover:scale-105 inline-block">
                  Book a visit
                </a>
             </li>
          </ul>
        </nav>
      </div>

      {/* DEMO Search Overlay */}
      {isSearchOpen && (
        <div className="fixed inset-0 bg-black/70 z-40 flex items-center justify-center" onClick={() => setIsSearchOpen(false)}>
            <div className="bg-white p-8 rounded-lg" onClick={e => e.stopPropagation()}>
                <h2 className="text-2xl font-bold mb-4">Search</h2>
                <input type="text" placeholder="Type to search..." className="w-full p-2 border rounded"/>
            </div>
        </div>
      )}
    </>
  );
};

export default Header;