import { useEffect, useRef, useState, useCallback } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

export const useScrollAnimation = (animationType = 'fadeUp', delay = 0) => {
  const ref = useRef()

  useEffect(() => {
    const element = ref.current
    if (!element) return

    // Set initial state based on animation type
    let fromVars = {}
    let toVars = {}

    switch (animationType) {
      case 'fadeUp':
        fromVars = { opacity: 0, y: 50 }
        toVars = { opacity: 1, y: 0 }
        break
      case 'fadeLeft':
        fromVars = { opacity: 0, x: -50 }
        toVars = { opacity: 1, x: 0 }
        break
      case 'fadeRight':
        fromVars = { opacity: 0, x: 50 }
        toVars = { opacity: 1, x: 0 }
        break
      case 'scale':
        fromVars = { opacity: 0, scale: 0.8 }
        toVars = { opacity: 1, scale: 1 }
        break
      default:
        fromVars = { opacity: 0, y: 30 }
        toVars = { opacity: 1, y: 0 }
    }

    // Set initial state with will-change for better performance
    gsap.set(element, { ...fromVars, willChange: 'transform, opacity' })

    // Create scroll trigger animation with improved settings
    const animation = gsap.to(element, {
      ...toVars,
      duration: 1.2,
      ease: "power3.out",
      delay: delay,
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        fastScrollEnd: true,
        preventOverlaps: true,
        refreshPriority: -1
      },
      onComplete: () => {
        // Remove will-change after animation completes
        gsap.set(element, { willChange: 'auto' })
      }
    })

    // Cleanup function
    return () => {
      if (animation.scrollTrigger) {
        animation.scrollTrigger.kill()
      }
      animation.kill()
      gsap.set(element, { willChange: 'auto' })
    }
  }, [animationType, delay])

  return ref
}

export const ScrollReveal = ({ 
  children, 
  className = '', 
  delay = 0, 
  animationType = 'fadeUp' 
}) => {
  const ref = useScrollAnimation(animationType, delay)

  return (
    <div ref={ref} className={`${className} transform-gpu`}>
      {children}
    </div>
  )
}

// Stagger animation for multiple elements
export const useStaggerAnimation = (selector, delay = 0.1) => {
  const containerRef = useRef()

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const elements = container.querySelectorAll(selector)
    if (elements.length === 0) return
    
    gsap.set(elements, { opacity: 0, y: 30, willChange: 'transform, opacity' })

    const animation = gsap.to(elements, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out",
      stagger: delay,
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse",
        fastScrollEnd: true,
        preventOverlaps: true
      },
      onComplete: () => {
        gsap.set(elements, { willChange: 'auto' })
      }
    })

    return () => {
      if (animation.scrollTrigger) {
        animation.scrollTrigger.kill()
      }
      animation.kill()
      gsap.set(elements, { willChange: 'auto' })
    }
  }, [selector, delay])

  return containerRef
}

// Horizontal scroll hook for parallax scrolling
export const useHorizontalScroll = (config = {}) => {
  const {
    containerRef,
    sectionsRef,
    onProgressChange,
    onComplete,
    enabled = true,
    mobileBreakpoint = 768,
    debugMode = false,
    scrollSensitivity = 1,
    debounceDelay = 16 // ~60fps
  } = config

  const [scrollState, setScrollState] = useState({
    progress: 0,
    currentSection: 0,
    isActive: false,
    direction: 'forward',
    isScrolling: false
  })

  const scrollTriggerRef = useRef(null)
  const lastProgressRef = useRef(0)
  const scrollTimeoutRef = useRef(null)
  const lastScrollTimeRef = useRef(0)

  const log = useCallback((message, data = '') => {
    if (debugMode) {
      console.log(`[HorizontalScroll] ${message}`, data)
    }
  }, [debugMode])

  // Debounced scroll state update
  const updateScrollState = useCallback((newState) => {
    const now = Date.now()

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    // Immediate update for critical state changes
    if (newState.isActive !== scrollState.isActive) {
      setScrollState(prev => ({ ...prev, ...newState }))
      lastScrollTimeRef.current = now
      return
    }

    // Debounce other updates
    if (now - lastScrollTimeRef.current >= debounceDelay) {
      setScrollState(prev => ({ ...prev, ...newState, isScrolling: true }))
      lastScrollTimeRef.current = now

      // Set scrolling to false after a delay
      scrollTimeoutRef.current = setTimeout(() => {
        setScrollState(prev => ({ ...prev, isScrolling: false }))
      }, 150)
    }
  }, [scrollState.isActive, debounceDelay])

  useEffect(() => {
    if (!enabled || !containerRef?.current || !sectionsRef?.current) {
      log('Hook disabled or refs not ready')
      return
    }

    // Check if we're on mobile
    const isMobile = window.innerWidth < mobileBreakpoint
    if (isMobile) {
      log('Mobile detected, skipping horizontal scroll')
      return
    }

    // Enhanced GSAP availability check with error handling
    try {
      if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
        throw new Error('GSAP or ScrollTrigger not available')
      }

      // Test GSAP functionality
      gsap.set({}, {})

      log('GSAP availability check passed')
    } catch (error) {
      console.error('[HorizontalScroll] GSAP Error:', error)
      log('GSAP availability check failed', error.message)
      return
    }

    const container = containerRef.current
    const sections = sectionsRef.current
    const sectionElements = sections.children

    if (sectionElements.length === 0) {
      log('No sections found')
      return
    }

    // Performance monitoring
    const performanceStart = performance.now()
    let frameCount = 0
    let lastFrameTime = performanceStart

    log('Initializing horizontal scroll', {
      container,
      sections,
      sectionCount: sectionElements.length
    })

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    if (prefersReducedMotion) {
      log('Reduced motion preference detected, using simplified animations')
    }

    // Calculate total width needed for horizontal scrolling
    const totalWidth = sectionElements.length * 100 // 100vw per section

    // Set up the horizontal layout with performance optimizations
    gsap.set(sections, {
      width: `${totalWidth}vw`,
      display: 'flex',
      willChange: prefersReducedMotion ? 'auto' : 'transform',
      force3D: !prefersReducedMotion
    })

    gsap.set(sectionElements, {
      width: '100vw',
      flexShrink: 0,
      willChange: prefersReducedMotion ? 'auto' : 'transform',
      force3D: !prefersReducedMotion
    })

    // Create the horizontal scroll animation with accessibility considerations
    const horizontalTween = gsap.to(sections, {
      x: () => -(totalWidth - 100) + 'vw',
      ease: prefersReducedMotion ? 'none' : 'power2.inOut',
      force3D: !prefersReducedMotion,
      duration: prefersReducedMotion ? 0 : undefined
    })

    // Create ScrollTrigger with enhanced configuration
    scrollTriggerRef.current = ScrollTrigger.create({
      trigger: container,
      start: 'top top',
      end: () => `+=${window.innerHeight * (sectionElements.length - 1) * 2}`, // More natural scroll distance
      pin: true,
      scrub: 1.2, // Slightly smoother scrub
      animation: horizontalTween,
      anticipatePin: 1,
      fastScrollEnd: true,
      preventOverlaps: true,
      onUpdate: (self) => {
        const progress = self.progress
        const exactSection = progress * (sectionElements.length - 1)
        const currentSection = Math.round(exactSection)
        const direction = progress > lastProgressRef.current ? 'forward' : 'backward'

        // Detect significant direction changes
        const progressDelta = Math.abs(progress - lastProgressRef.current)
        const isSignificantChange = progressDelta > 0.001

        lastProgressRef.current = progress

        const newState = {
          progress,
          currentSection: Math.min(Math.max(currentSection, 0), sectionElements.length - 1),
          isActive: progress > 0.01 && progress < 0.99,
          direction
        }

        // Use debounced update for smooth performance
        updateScrollState(newState)

        if (onProgressChange && isSignificantChange) {
          onProgressChange(progress)
        }

        if (isSignificantChange) {
          log('Scroll update', newState)
        }
      },
      onEnter: () => {
        log('Entering horizontal scroll zone')
        document.body.style.overflow = 'hidden'
      },
      onLeave: () => {
        log('Leaving horizontal scroll zone')
        document.body.style.overflow = 'auto'
      },
      onComplete: () => {
        log('Horizontal scroll complete')
        document.body.style.overflow = 'auto'
        if (onComplete) {
          onComplete()
        }
      },
      invalidateOnRefresh: true,
      refreshPriority: -1
    })

    log('ScrollTrigger created', scrollTriggerRef.current)

    // Cleanup function
    return () => {
      log('Cleaning up horizontal scroll')
      if (scrollTriggerRef.current) {
        scrollTriggerRef.current.kill()
        scrollTriggerRef.current = null
      }

      // Clear timeouts
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }

      // Reset body overflow
      document.body.style.overflow = 'auto'

      // Reset styles
      gsap.set([sections, ...sectionElements], {
        clearProps: 'all'
      })
    }
  }, [enabled, containerRef, sectionsRef, onProgressChange, onComplete, mobileBreakpoint, debugMode, log, updateScrollState])

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      const isMobile = window.innerWidth < mobileBreakpoint
      if (isMobile && scrollTriggerRef.current) {
        log('Switching to mobile, killing ScrollTrigger')
        scrollTriggerRef.current.kill()
        scrollTriggerRef.current = null
      } else if (!isMobile && !scrollTriggerRef.current && enabled) {
        log('Switching to desktop, refreshing ScrollTrigger')
        ScrollTrigger.refresh()
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [mobileBreakpoint, enabled, log])

  return scrollState
}

