import { useEffect, useRef } from 'react'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

// Register ScrollTrigger plugin
gsap.registerPlugin(ScrollTrigger)

export const useScrollAnimation = (animationType = 'fadeUp', delay = 0) => {
  const ref = useRef()

  useEffect(() => {
    const element = ref.current
    if (!element) return

    // Set initial state based on animation type
    let fromVars = {}
    let toVars = {}

    switch (animationType) {
      case 'fadeUp':
        fromVars = { opacity: 0, y: 50 }
        toVars = { opacity: 1, y: 0 }
        break
      case 'fadeLeft':
        fromVars = { opacity: 0, x: -50 }
        toVars = { opacity: 1, x: 0 }
        break
      case 'fadeRight':
        fromVars = { opacity: 0, x: 50 }
        toVars = { opacity: 1, x: 0 }
        break
      case 'scale':
        fromVars = { opacity: 0, scale: 0.8 }
        toVars = { opacity: 1, scale: 1 }
        break
      default:
        fromVars = { opacity: 0, y: 30 }
        toVars = { opacity: 1, y: 0 }
    }

    // Set initial state with will-change for better performance
    gsap.set(element, { ...fromVars, willChange: 'transform, opacity' })

    // Create scroll trigger animation with improved settings
    const animation = gsap.to(element, {
      ...toVars,
      duration: 1.2,
      ease: "power3.out",
      delay: delay,
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        fastScrollEnd: true,
        preventOverlaps: true,
        refreshPriority: -1
      },
      onComplete: () => {
        // Remove will-change after animation completes
        gsap.set(element, { willChange: 'auto' })
      }
    })

    // Cleanup function
    return () => {
      if (animation.scrollTrigger) {
        animation.scrollTrigger.kill()
      }
      animation.kill()
      gsap.set(element, { willChange: 'auto' })
    }
  }, [animationType, delay])

  return ref
}

export const ScrollReveal = ({ 
  children, 
  className = '', 
  delay = 0, 
  animationType = 'fadeUp' 
}) => {
  const ref = useScrollAnimation(animationType, delay)

  return (
    <div ref={ref} className={`${className} transform-gpu`}>
      {children}
    </div>
  )
}

// Stagger animation for multiple elements
export const useStaggerAnimation = (selector, delay = 0.1) => {
  const containerRef = useRef()

  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    const elements = container.querySelectorAll(selector)
    if (elements.length === 0) return
    
    gsap.set(elements, { opacity: 0, y: 30, willChange: 'transform, opacity' })

    const animation = gsap.to(elements, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out",
      stagger: delay,
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse",
        fastScrollEnd: true,
        preventOverlaps: true
      },
      onComplete: () => {
        gsap.set(elements, { willChange: 'auto' })
      }
    })

    return () => {
      if (animation.scrollTrigger) {
        animation.scrollTrigger.kill()
      }
      animation.kill()
      gsap.set(elements, { willChange: 'auto' })
    }
  }, [selector, delay])

  return containerRef
}

