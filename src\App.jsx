import React from 'react'
import './App.css'
import Header from './components/Header'
import HeroSection from './components/HeroSection'
import MudSection from './components/MudSection'
import ExploreSection from './components/ExploreSection'
import ValuesSection from './components/ValuesSection'
import HorizontalScrollContainer from './components/HorizontalScrollContainer'
import HeadWelcomeSection from './components/HeadWelcomeSection'
import SevenCsSection from './components/SevenCsSection'
import Year78Section from './components/Year78Section'
import AnnouncementsSection from './components/AnnouncementsSection'
import TestimonialsSection from './components/TestimonialsSection'
import SocialSection from './components/SocialSection'
import StatsSection from './components/StatsSection'
import ContactSection from './components/ContactSection'
import Footer from './components/Footer'

function App() {
  const handleHorizontalProgress = (progress) => {
    // Optional: Add any global progress handling here
    console.log('Horizontal scroll progress:', progress)
  }

  const handleHorizontalComplete = () => {
    // Optional: Add any completion handling here
    console.log('Horizontal scroll completed')
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <HeroSection />
        <MudSection />
        <ExploreSection />
        <ValuesSection />

        {/* Horizontal Parallax Scrolling Container */}
        <HorizontalScrollContainer
          showProgressIndicator={true}
          debugMode={false} // Set to true for development
          onProgressChange={handleHorizontalProgress}
          onComplete={handleHorizontalComplete}
        >
          <HeadWelcomeSection />
          <SevenCsSection />
          <Year78Section />
        </HorizontalScrollContainer>

        <AnnouncementsSection />
        <TestimonialsSection />
        <SocialSection />
        <StatsSection />
        <ContactSection />
      </main>
      <Footer />
    </div>
  )
}

export default App

