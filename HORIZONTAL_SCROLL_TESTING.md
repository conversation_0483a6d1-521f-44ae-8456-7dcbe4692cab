# Horizontal Parallax Scrolling - Testing Guide

## Overview
This document provides comprehensive testing instructions for the horizontal parallax scrolling feature implemented in the Great Walstead website clone.

## Automated Tests

### Running Tests
```bash
# Run all tests
npm test

# Run specific test files
npm test HorizontalScrollContainer.test.jsx
npm test ScrollProgressIndicator.test.jsx
npm test HorizontalScrollIntegration.test.jsx

# Run tests in watch mode
npm test -- --watch
```

### Test Coverage
- **Unit Tests**: Individual component functionality
- **Integration Tests**: Complete horizontal scroll experience
- **Error Boundary Tests**: Graceful error handling
- **Accessibility Tests**: ARIA attributes and keyboard navigation

## Manual Testing Checklist

### Desktop Testing (Chrome, Firefox, Safari, Edge)

#### Basic Functionality
- [ ] Page loads without errors
- [ ] Horizontal scroll container is visible after ValuesSection
- [ ] Three sections (Head's Welcome, 7Cs, Year 7&8) scroll horizontally
- [ ] Smooth transition from vertical to horizontal scrolling
- [ ] Smooth transition from horizontal back to vertical scrolling
- [ ] AnnouncementsSection appears after horizontal scroll completes

#### Scroll Behavior
- [ ] Mouse wheel scrolling triggers horizontal movement
- [ ] Scroll direction detection works (forward/backward)
- [ ] Sections pin properly during horizontal scroll phase
- [ ] No vertical scrolling during horizontal phase
- [ ] Smooth easing and animation timing (60fps)

#### Progress Indicator
- [ ] Progress indicator appears when horizontal scrolling starts
- [ ] Dots highlight correctly for current section
- [ ] Smooth transitions between section indicators
- [ ] Progress indicator hides when horizontal scrolling completes
- [ ] Section labels are visible and correct

#### Keyboard Navigation
- [ ] Tab to focus on horizontal scroll container
- [ ] Arrow Left/Right keys navigate between sections
- [ ] Home key goes to first section
- [ ] End key goes to last section
- [ ] Focus management works properly

### Mobile Testing (iOS Safari, Chrome Mobile, Samsung Internet)

#### Mobile Fallback
- [ ] Horizontal scrolling is disabled on screens < 768px
- [ ] Sections display in vertical layout on mobile
- [ ] All section content remains accessible
- [ ] No JavaScript errors on mobile devices
- [ ] Touch scrolling works normally (vertical)

#### Touch Gestures (Desktop with touch)
- [ ] Horizontal swipe gestures are detected
- [ ] Touch events don't interfere with vertical scrolling
- [ ] Smooth touch interactions

### Accessibility Testing

#### Screen Reader Support
- [ ] ARIA labels are properly announced
- [ ] Section navigation is accessible
- [ ] Screen reader instructions are available
- [ ] Focus management works with assistive technology

#### Reduced Motion
- [ ] `prefers-reduced-motion: reduce` is respected
- [ ] Animations are simplified or disabled when requested
- [ ] Functionality remains intact with reduced motion

### Performance Testing

#### Animation Performance
- [ ] Consistent 60fps during scroll animations
- [ ] No frame drops or stuttering
- [ ] Smooth performance on lower-end devices
- [ ] Memory usage remains stable

#### Loading Performance
- [ ] No significant impact on initial page load
- [ ] GSAP loads properly
- [ ] ScrollTrigger initializes without errors

### Error Handling Testing

#### GSAP Availability
- [ ] Graceful fallback when GSAP fails to load
- [ ] Error boundary catches animation errors
- [ ] Fallback to vertical scrolling works
- [ ] Console warnings are appropriate

#### Browser Compatibility
- [ ] Works in Chrome 90+
- [ ] Works in Firefox 88+
- [ ] Works in Safari 14+
- [ ] Works in Edge 90+
- [ ] Graceful degradation in older browsers

## Debug Mode Testing

### Enable Debug Mode
Set `debugMode={true}` in the HorizontalScrollContainer component in App.jsx:

```jsx
<HorizontalScrollContainer
  showProgressIndicator={true}
  debugMode={true} // Enable debug mode
  onProgressChange={handleHorizontalProgress}
  onComplete={handleHorizontalComplete}
>
```

### Debug Features
- [ ] Debug overlay shows current progress
- [ ] Section numbers are displayed correctly
- [ ] Active state is tracked properly
- [ ] Direction changes are logged
- [ ] Console logs provide useful information

## Cross-Browser Compatibility

### Desktop Browsers
- [ ] **Chrome 90+**: Full functionality
- [ ] **Firefox 88+**: Full functionality
- [ ] **Safari 14+**: Full functionality
- [ ] **Edge 90+**: Full functionality

### Mobile Browsers
- [ ] **iOS Safari**: Vertical fallback works
- [ ] **Chrome Mobile**: Vertical fallback works
- [ ] **Samsung Internet**: Vertical fallback works

## Performance Benchmarks

### Target Metrics
- **Animation FPS**: 60fps consistently
- **Memory Usage**: < 50MB additional
- **Load Time Impact**: < 200ms additional
- **Scroll Responsiveness**: < 16ms per frame

### Monitoring Tools
- Chrome DevTools Performance tab
- Firefox Developer Tools Performance
- Safari Web Inspector Timelines

## Known Issues and Limitations

### Current Limitations
- Horizontal scrolling disabled on mobile (by design)
- Requires modern browser with GSAP support
- May not work with certain browser extensions that modify scrolling

### Workarounds
- Mobile users get vertical layout (accessible alternative)
- Error boundary provides fallback for GSAP issues
- Progressive enhancement ensures basic functionality

## Reporting Issues

When reporting issues, please include:
1. Browser and version
2. Device type and screen size
3. Steps to reproduce
4. Expected vs actual behavior
5. Console errors (if any)
6. Debug mode information (if available)

## Success Criteria

The horizontal parallax scrolling feature is considered successful when:
- All manual tests pass across target browsers
- Automated tests have >90% coverage
- Performance meets target benchmarks
- Accessibility requirements are met
- Error handling works gracefully
- Mobile fallback provides good user experience
