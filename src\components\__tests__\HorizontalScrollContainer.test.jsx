import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import HorizontalScrollContainer from '../HorizontalScrollContainer'

// Mock GSAP
jest.mock('gsap', () => ({
  gsap: {
    set: jest.fn(),
    to: jest.fn(() => ({ kill: jest.fn() })),
    registerPlugin: jest.fn(),
  },
  ScrollTrigger: {
    create: jest.fn(() => ({ kill: jest.fn() })),
    refresh: jest.fn(),
  }
}))

// Mock components
const TestSection1 = () => <div data-testid="section-1">Section 1</div>
const TestSection2 = () => <div data-testid="section-2">Section 2</div>
const TestSection3 = () => <div data-testid="section-3">Section 3</div>

describe('HorizontalScrollContainer', () => {
  beforeEach(() => {
    // Reset window size to desktop
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    })
    
    // Mock matchMedia for prefers-reduced-motion
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    })
  })

  test('renders children in desktop mode', () => {
    render(
      <HorizontalScrollContainer>
        <TestSection1 />
        <TestSection2 />
        <TestSection3 />
      </HorizontalScrollContainer>
    )

    expect(screen.getByTestId('section-1')).toBeInTheDocument()
    expect(screen.getByTestId('section-2')).toBeInTheDocument()
    expect(screen.getByTestId('section-3')).toBeInTheDocument()
  })

  test('renders children in mobile mode', () => {
    // Mock mobile viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 600,
    })

    render(
      <HorizontalScrollContainer>
        <TestSection1 />
        <TestSection2 />
        <TestSection3 />
      </HorizontalScrollContainer>
    )

    expect(screen.getByTestId('section-1')).toBeInTheDocument()
    expect(screen.getByTestId('section-2')).toBeInTheDocument()
    expect(screen.getByTestId('section-3')).toBeInTheDocument()
  })

  test('handles keyboard navigation', async () => {
    render(
      <HorizontalScrollContainer debugMode={true}>
        <TestSection1 />
        <TestSection2 />
        <TestSection3 />
      </HorizontalScrollContainer>
    )

    const container = screen.getByRole('region')
    
    // Test arrow key navigation
    fireEvent.keyDown(container, { key: 'ArrowRight' })
    fireEvent.keyDown(container, { key: 'ArrowLeft' })
    fireEvent.keyDown(container, { key: 'Home' })
    fireEvent.keyDown(container, { key: 'End' })

    // Should not throw errors
    expect(container).toBeInTheDocument()
  })

  test('handles touch events', () => {
    render(
      <HorizontalScrollContainer>
        <TestSection1 />
        <TestSection2 />
        <TestSection3 />
      </HorizontalScrollContainer>
    )

    const container = screen.getByRole('region')
    
    // Simulate touch events
    fireEvent.touchStart(container, {
      touches: [{ clientX: 100, clientY: 100 }]
    })
    
    fireEvent.touchMove(container, {
      touches: [{ clientX: 50, clientY: 100 }]
    })
    
    fireEvent.touchEnd(container)

    expect(container).toBeInTheDocument()
  })

  test('shows progress indicator when enabled', () => {
    render(
      <HorizontalScrollContainer showProgressIndicator={true}>
        <TestSection1 />
        <TestSection2 />
        <TestSection3 />
      </HorizontalScrollContainer>
    )

    // Progress indicator should be present (though may be hidden initially)
    expect(document.querySelector('.fixed.bottom-8')).toBeInTheDocument()
  })

  test('handles error boundary', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {})

    const ThrowError = () => {
      throw new Error('Test error')
    }

    render(
      <HorizontalScrollContainer>
        <ThrowError />
      </HorizontalScrollContainer>
    )

    // Should render fallback UI instead of crashing
    expect(screen.getByText(/Scroll Animation Unavailable/i)).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })

  test('respects debug mode', () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {})

    render(
      <HorizontalScrollContainer debugMode={true}>
        <TestSection1 />
      </HorizontalScrollContainer>
    )

    // Debug info should be visible
    expect(screen.getByText(/Progress:/)).toBeInTheDocument()
    
    consoleSpy.mockRestore()
  })
})
