@import "tailwindcss";
/* Google Fonts - Oxygen */
@import url('https://fonts.googleapis.com/css2?family=Oxygen:wght@300;400;700&display=swap');

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Custom font face for TrueNorthRoughBlack */
@font-face {
  font-family: "TrueNorthRoughBlack";
  src: url('./assets/fonts/TrueNorthRoughBlack.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* Custom CSS Variables for Great Walstead */
:root {
  --header-height: 94px;
  --header-space: 10rem;
  --gw-navy: #1a2b4a;
  --gw-pink: #e91e63;
  --gw-white: #ffffff;
  --gw-light-gray: #f5f5f5;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}


/* Global styles for Great Walstead */
html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: 'Oxygen', sans-serif;
  margin: 0;
  padding: 0;
  /* Improve scrolling performance */
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
}

/* Performance optimizations for smooth scrolling */
* {
  box-sizing: border-box;
}

/* Optimize transforms and animations */
.transform-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Custom utility classes */
.font-truenorth {
  font-family: "TrueNorthRoughBlack", sans-serif;
}

.h-screen-d {
  height: 100vh;
  height: 100dvh;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  cursor: pointer;
}

/* SVG styles */
svg {
  max-height: 100%;
  max-width: 100%;
}

/* Strong/bold text normalization */
b,
strong {
  font-weight: normal;
}

/* Input number styles */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
}

/* Custom animations and transitions */
.scroll-down-indicator {
  animation: bounce 2s infinite;
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-10px);
  }

  60% {
    transform: translateY(-5px);
  }
}

/* Loading animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Gravity Hero Section Styles */
@import url('https://fonts.googleapis.com/css?family=Lato:300,400,700');

.gravity-hero-container {
  position: relative;
  height: 100vh;
  width: 100%;
  font-family: 'Lato', sans-serif;
  color: #FFF;
  background: radial-gradient(ellipse at bottom, #0C1116 0%, #090a0f 100%);
  overflow: hidden;
}

.gravity-stars {
  width: 1px;
  height: 1px;
  background: transparent;
  animation: animStar 50s linear infinite;
}

.gravity-stars:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 1px;
  height: 1px;
  background: transparent;
  box-shadow: inherit;
}

.gravity-stars2 {
  width: 2px;
  height: 2px;
  background: transparent;
  animation: animStar 100s linear infinite;
}

.gravity-stars2:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 2px;
  height: 2px;
  background: transparent;
  box-shadow: inherit;
}

.gravity-stars3 {
  width: 3px;
  height: 3px;
  background: transparent;
  animation: animStar 150s linear infinite;
}

.gravity-stars3:after {
  content: " ";
  position: absolute;
  top: 2000px;
  width: 3px;
  height: 3px;
  background: transparent;
  box-shadow: inherit;
}

.gravity-horizon {
  position: absolute;
  width: 160%;
  height: 70%;
  border-radius: 100% / 100%;
  background: #038bff;
  filter: blur(30px);
  left: 50%;
  bottom: -20%;
  margin-left: -80%;
}

.gravity-horizon:before {
  content: " ";
  position: absolute;
  width: 81.25%;
  height: 70%;
  border-radius: 100% / 100%;
  background: #51AFFF;
  filter: blur(30px);
  opacity: 0.6;
  margin-left: 9.375%;
}

.gravity-horizon:after {
  content: " ";
  position: absolute;
  width: 32%;
  height: 20%;
  border-radius: 650px / 350px;
  background: #B0DAFF;
  filter: blur(30px);
  opacity: 0.5;
  margin-left: 34%;
}

.gravity-glow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 100% / 100%;
  background: #215496;
  filter: blur(200px);
  opacity: 0.7;
  top: -10%;
}

.gravity-earth {
  position: absolute;
  width: 200%;
  height: 100%;
  background: black;
  border-radius: 100% / 100%;
  left: 50%;
  bottom: -50%;
  margin-left: -100%;
}

.gravity-earth:before {
  content: " ";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 100% / 100%;
  box-shadow: inset 0px 0px 62px 20px rgba(60, 105, 138, 0.85);
}

.gravity-earth:after {
  content: " ";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 100% / 100%;
  background: linear-gradient(to right, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 50%, rgba(0, 0, 0, 1) 100%);
}

.gravity-title {
  position: absolute;
  font-weight: 300;
  top: 36%;
  left: 0;
  right: 0;
  margin-top: -80px;
  font-size: 20px;
  text-align: center;
  letter-spacing: 5px;
  padding-left: 20px;
  background: linear-gradient(to bottom, white, rgb(219, 221, 224), #38495a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: animGravity 6s ease infinite;
}

.gravity-subtitle {
  position: absolute;
  font-weight: 300;
  top: 70%;
  left: 0;
  right: 0;
  font-size: 25px;
  text-align: center;
  letter-spacing: 6px;
}

.gravity-subtitle span {
  color: rgb(216, 216, 216);
  animation-duration: 6s;
  animation-iteration-count: infinite;
  animation-timing-function: ease;
}

.gravity-subtitle span:nth-child(1) {
  animation-name: animDont;
}

.gravity-subtitle span:nth-child(2) {
  animation-name: animLet;
}

.gravity-subtitle span:nth-child(3) {
  animation-name: animGo;
}

.gravity-subtitle span:nth-child(4) {
  animation-name: animGentle;
}

@keyframes animGentle {

  0%,
  45% {
    transform: translateY(-26px);
    opacity: 0;
  }

  65%,
  80% {
    transform: translateY(0px);
    opacity: 1;
  }

  92%,
  100% {
    transform: translateY(-4px);
    opacity: 0;
  }
}

@keyframes animStar {
  from {
    transform: translateY(0px);
  }

  to {
    transform: translateY(-2000px);
  }
}

@keyframes animGravity {
  0% {
    transform: translateY(-26px);
    opacity: 0;
  }

  30%,
  80% {
    letter-spacing: 40px;
    padding-left: 40px;
    transform: translateY(0px);
    opacity: 1;
  }

  92%,
  100% {
    letter-spacing: 35px;
    padding-left: 35px;
    transform: translateY(-4px);
    opacity: 0;
  }
}

@keyframes animDont {

  0%,
  15% {
    transform: translateY(-26px);
    opacity: 0;
  }

  35%,
  80% {
    transform: translateY(0px);
    opacity: 1;
  }

  92%,
  100% {
    transform: translateY(-4px);
    opacity: 0;
  }
}

@keyframes animLet {

  0%,
  25% {
    transform: translateY(-26px);
    opacity: 0;
  }

  45%,
  80% {
    transform: translateY(0px);
    opacity: 1;
  }

  92%,
  100% {
    transform: translateY(-4px);
    opacity: 0;
  }
}

@keyframes animGo {

  0%,
  35% {
    transform: translateY(-26px);
    opacity: 0;
  }

  55%,
  80% {
    transform: translateY(0px);
    opacity: 1;
  }

  92%,
  100% {
    transform: translateY(-4px);
    opacity: 0;
  }
}

@media (max-width: 768px) {
  .gravity-title {
    font-size: 60px;
    letter-spacing: 10px;
    padding-left: 10px;
  }

  .gravity-subtitle {
    font-size: 18px;
    letter-spacing: 3px;
  }
}

/* Wave animations */
@keyframes wave {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }
  50% {
    transform: translateX(-25%) translateZ(0) scaleY(0.55);
  }
  100% {
    transform: translateX(-50%) translateZ(0) scaleY(1);
  }
}

@keyframes wave-reverse {
  0% {
    transform: translateX(0) translateZ(0) scaleY(1);
  }
  50% {
    transform: translateX(25%) translateZ(0) scaleY(0.55);
  }
  100% {
    transform: translateX(50%) translateZ(0) scaleY(1);
  }
}

.animate-wave {
  animation: wave 10s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite;
  transform-origin: 50% 50%;
}

.animate-wave-reverse {
  animation: wave-reverse 8s cubic-bezier(0.36, 0.45, 0.63, 0.53) infinite;
  transform-origin: 50% 50%;
}