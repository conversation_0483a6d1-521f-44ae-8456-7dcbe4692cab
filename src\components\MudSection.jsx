import React, { useRef, useEffect } from 'react';
import { ScrollReveal } from './ScrollAnimations';
import { gsap } from 'gsap';
import mudImage from '../assets/images/Vector-Smart-Object.jpg';

const MudSection = () => {
  const loadingRef = useRef();
  const sectionRef = useRef();

  useEffect(() => {
    // Only animate if the ref exists
    if (loadingRef.current) {
      const animation = gsap.to(loadingRef.current, {
        rotation: 360,
        duration: 2,
        ease: "none",
        repeat: -1
      });

      // Cleanup animation on unmount
      return () => {
        animation.kill();
      };
    }
  }, []);

  return (
    <section ref={sectionRef} className="relative bg-[#FF2C2C] will-change-transform">
      
      {/* Wavy SVG at the top - Fixed positioning issues */}
      <div className="absolute top-0 left-0 w-full overflow-hidden leading-[0] z-10">
        <svg 
          className="relative block w-full h-24 transform-gpu" 
          viewBox="0 0 1440 120" 
          preserveAspectRatio="none"
        >
          <path 
            d="M0,40 C150,90 350,10 600,50 C850,90 1050,10 1440,60 L1440,0 L0,0 Z" 
            fill="#FFFFFF"
          ></path>
        </svg>
      </div>
      
      <div className="relative pt-24 pb-16 z-20"> {/* Added relative positioning and z-index */}
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <ScrollReveal animationType="fadeRight" delay={0.1}>
              <div className="transform-gpu">
                <h2 className=" font-['TrueNorthRoughBlack'] text-4xl md:text-5xl font-bold text-white mb-6">
                  Mud π - The learning philosophy at the heart of our school
                </h2>
                <p className="text-lg text-white/90 mb-8">
                  Den building or structural engineering? Sandpit or blank canvas?
                  Discover how we maximise our environment, bringing every lesson to life.
                </p>
               
              </div>
            </ScrollReveal>
            
            {/* Right side - Image */}
            <ScrollReveal animationType="fadeLeft" delay={0.3}>
              <div className="relative transform-gpu">
                <div className="aspect-video bg-gray-200 rounded-lg overflow-hidden transition-transform duration-300 shadow-lg hover:scale-105">
                  <img
                    src={mudImage}
                    alt="Children learning in nature - mud play and outdoor education"
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    
                    <div className="text-sm font-medium">Learning in Nature</div>
                  </div>
                </div>
              </div>
            </ScrollReveal>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MudSection;